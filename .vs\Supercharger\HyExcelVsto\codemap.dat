<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectAllTimeMostUsedData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>DataCacheManager#公共方法#IsFirstOccurrence</CodeMapItemPath><ParameterList>string, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>DataCacheManager#公共方法#RebuildRowMappingOnly</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>RowDataCache#RowNumber</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>RowDataCache#SectorName</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>RowDataCache#StationName</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>RowDataCache#FrequencyBand</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ApiClient#PostJsonObjectAsync</CodeMapItemPath><ParameterList>string, string, string, int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ApiClient#UploadFileAsync</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ApiClient#UploadFileJsonObjectAsync</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ZnRibbonClass#buttonPPT转PDF_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ZnRibbonClass#button文件操作_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorHelper#核心处理执行#ExecuteProcessingAsync</CodeMapItemPath><ParameterList>Worksheet, Range, string, System.Action&lt;string&gt;, IProgress&lt;ProgressEventArgs&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorHelper#核心处理执行#CancelProcessing</CodeMapItemPath><ParameterList>Core.StationDataProcessor</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法#CreateThreadSafeLogWriter</CodeMapItemPath><ParameterList>ETLogDisplayControl, Form</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法#CreateThreadSafeMessageHandler</CodeMapItemPath><ParameterList>Form</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法#ValidateBasicWorksheetAndRange</CodeMapItemPath><ParameterList>Worksheet, System.Action&lt;string, string, MessageBoxIcon&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorHelper#进度处理辅助#ProgressUpdateInfo#ProgressPercentage</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ThisAddIn.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#定义变量#MenuManagerInstance</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#定义变量#TopMostForm</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#ThisAddIn_Startup</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#EnsureMenuInitialized</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#InitializeTopForm</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#RegisterWorksheetHandlers</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#RegisterCellHandlers</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#LoadConfigurationAndAuthorization</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#定时设置为只读#AddToLockFile</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#定时设置为只读#LockFileTimer_Tick</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#App单元格事件#App_SheetBeforeRightClick</CodeMapItemPath><ParameterList>object, Range, bool</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#窗体相关#OpenFormNames</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#窗体相关#OpenForm</CodeMapItemPath><ParameterList>Form, XlFormPosition, bool</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ThisAddIn#窗体相关#NotifyFormsSelectionChange</CodeMapItemPath><ParameterList>Range, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HyLicenseManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#实现抽象方法#GetLicenseControllerConfig</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#授权控制器初始化#InitializeLicenseController</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#权限管理器初始化#InitializePermissionManagers</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#权限管理器初始化#InitializeAuthorization</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#权限检查方法#HasPermission</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#权限刷新方法#ForceRefreshPermissionsAndUIInternal</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#权限刷新方法#ForceRefreshPermissionsAndUIAsync</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#用户组管理#RefreshCurrentUserGroupsInternal</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#用户组管理#LoadUserGroupsFromConfig</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#网络授权更新回调#OnNetworkLicenseUpdated</CodeMapItemPath><ParameterList>ETLicenseInfo, string</ParameterList><UsageCount>8</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#授权状态刷新#RefreshAuthorizationAsyncInternal</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#定时器和设置初始化#InitializeTimerAndSettings</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#公共属性#PermissionManager</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyLicenseManager#公共属性#UIPermissionManager</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#ValidateDataAsync</CodeMapItemPath><ParameterList>Worksheet, ProcessingOptions</ParameterList><UsageCount>14</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#ProcessNewColumnsAsync</CodeMapItemPath><ParameterList/><UsageCount>18</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#AddStatisticsColumnsWithRowContext</CodeMapItemPath><ParameterList/><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#PerformIntermediateSortAsync</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateStationName</CodeMapItemPath><ParameterList>object[]</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateFrequencyBand</CodeMapItemPath><ParameterList>object[]</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateStationFrequencyIndex</CodeMapItemPath><ParameterList>object[]</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateLogicalStationFromCache</CodeMapItemPath><ParameterList>object[]</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#FindColumnValue</CodeMapItemPath><ParameterList>object[], string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#IsFirstOccurrence</CodeMapItemPath><ParameterList>string, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CountUniqueEquipmentByStationFrequencyIndexFromCurrentRow</CodeMapItemPath><ParameterList>string, int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#HasDuplicateAzimuthFromCurrentRow</CodeMapItemPath><ParameterList>string, int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#GetRowDataFromDataAccess</CodeMapItemPath><ParameterList>int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#GetCurrentProcessingRow</CodeMapItemPath><ParameterList>object[]</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CountRowsByStationFrequencyIndexFallback</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CountUniqueEquipmentByStationFrequencyIndexFallback</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#GetMergedValuesByStationFrequencyIndexFromCurrentRow</CodeMapItemPath><ParameterList>string, string, int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateStatisticsValueWithRowContextAndCache</CodeMapItemPath><ParameterList>string, object[], int, Dictionary&lt;int, CalculationResults&gt;</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateBasicDataWithCache</CodeMapItemPath><ParameterList>object[], int, CalculationResults</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateHasPowerSplitterFromCache</CodeMapItemPath><ParameterList>CalculationResults</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateAnomalyWarningFromCache</CodeMapItemPath><ParameterList>object[], int, CalculationResults</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateDowntiltMergedWithRowContext</CodeMapItemPath><ParameterList>object[], int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateAzimuthMergedWithRowContext</CodeMapItemPath><ParameterList>object[], int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#CalculateAntennaHeightMergedWithRowContext</CodeMapItemPath><ParameterList>object[], int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#GetMergedValuesByStationFrequencyIndexFallback</CodeMapItemPath><ParameterList>string, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#私有方法#ReportProgress</CodeMapItemPath><ParameterList>int, string, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#xlApp</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#InitializeConfigurationSettings</CodeMapItemPath><ParameterList/><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#hyRibbon1_Load</CodeMapItemPath><ParameterList>object, RibbonUIEventArgs</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#动态控件标题管理#SaveOriginalControlTitles</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#动态控件标题管理#GetAllRibbonControlReferences</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#动态控件标题管理#IsRibbonControlField</CodeMapItemPath><ParameterList>System.Reflection.FieldInfo</ParameterList><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#控件标题更正方法#CorrectControlTitles</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#控件标题更正方法#OnNetworkLicenseUpdated</CodeMapItemPath><ParameterList/><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#控件标题更正方法#RefreshControlTitles</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#控件标题更正方法#OnPermissionManagerInitialized</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#hyTab#显示行列高亮控制</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#hyTab2#脚本#button打开脚本表_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#hyTab2#button工作表管理_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>9</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#hyTab2#checkBoxStockHelper_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#hyTab2#button考勤_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#znTab &amp; hyTab#buttonHy无线小工具_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyRibbonClass#znTab &amp; hyTab#buttonHy51小工具V2_Click</CodeMapItemPath><ParameterList>object, RibbonControlEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>IExcelDataAccess#SetAutoFilterAsync</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>IExcelDataAccess#GetDataRowCount</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>IExcelDataAccess#IsColumnExists</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\frm站点数据转换.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\frm站点数据转换.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HyUIPermissionManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>HyPermissionKeys#User</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#RegisterPermissionUIMappings</CodeMapItemPath><ParameterList/><UsageCount>13</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#SetDefaultUIVisibility</CodeMapItemPath><ParameterList>bool</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#GetPermissionUIActions</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#GetPermissionDisplayName</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>8</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#CreateControlPermissionManager</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#RegisterControlPermissionMappings</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#LogPermissionStatus</CodeMapItemPath><ParameterList>string, bool, DateTime?</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#Initialize</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#ForceRefreshPermissionsAndUI</CodeMapItemPath><ParameterList/><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#ForceInitializeGlobalMappings</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#RegisterSpecialPermissionControls</CodeMapItemPath><ParameterList>Dictionary&lt;string, (string PermissionKey, string NormalTitle)&gt;, Dictionary&lt;string, string&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#GetGlobalControlTitleMapping</CodeMapItemPath><ParameterList/><UsageCount>7</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#GetGlobalControlPermissionMapping</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#NotifyRibbonPermissionManagerInitialized</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法#OnRefreshRibbonControlTitles</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法#GetControlNormalTitleFallback</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法#GetControlPermissionKey</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>GlobalSettings.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Extensions\ZnWireless.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\ZnWireless.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorForm#处理方法#StartProcessingAsync</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorForm#处理方法#SetProcessingState</CodeMapItemPath><ParameterList>bool, bool</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorConfig#配置属性 - 直接替换原有硬编码#SUFFIX_PATTERNS</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorConfig#配置属性 - 直接替换原有硬编码#SOURCE_COLUMNS</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorConfig#初始化方法#LoadAllConfigurations</CodeMapItemPath><ParameterList>ETSectionConfigReader</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ValidationResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ValidationResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有字段#_dataCache</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#数据缓存方法#LoadDataCache</CodeMapItemPath><ParameterList>bool</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#数据缓存方法#ClearDataCache</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#数据缓存方法#GetCellValueFromCache</CodeMapItemPath><ParameterList>int, int</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#数据缓存方法#GetRowDataFromCache</CodeMapItemPath><ParameterList>int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#数据缓存方法#GetRowDataDirect</CodeMapItemPath><ParameterList>int</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#HideColumnsByHeadersAsync</CodeMapItemPath><ParameterList>string[]</ParameterList><UsageCount>11</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#ShowOnlySpecifiedColumnsAsync</CodeMapItemPath><ParameterList>string[]</ParameterList><UsageCount>7</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#GetAllColumnHeaders</CodeMapItemPath><ParameterList/><UsageCount>8</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#SortByColumnAsync</CodeMapItemPath><ParameterList>string, bool</ParameterList><UsageCount>14</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#SortByMultipleColumnsAsync</CodeMapItemPath><ParameterList>List&lt;(string columnHeader, bool ascending)&gt;</ParameterList><UsageCount>7</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#PerformRobustSort</CodeMapItemPath><ParameterList>Range, List&lt;(Range column, bool ascending, string name)&gt;</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#PerformVBAStyleSort</CodeMapItemPath><ParameterList>Range, List&lt;(Range column, bool ascending, string name)&gt;</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#PerformFallbackSort</CodeMapItemPath><ParameterList>Range, List&lt;(Range column, bool ascending, string name)&gt;</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#RefreshExcelDataAsync</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#AddCalculatedColumnsAsync</CodeMapItemPath><ParameterList>Dictionary&lt;string, Func&lt;object[], object&gt;&gt;</ParameterList><UsageCount>12</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#SetAutoFilterAsync</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#GetDataRowCount</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#IsColumnExists</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>7</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#SetCellValue</CodeMapItemPath><ParameterList>int, int, object</ParameterList><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#GetCellValue</CodeMapItemPath><ParameterList>int, int</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#InitializeRanges</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#GetLastColumnIndexPrivate</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#GetRowData</CodeMapItemPath><ParameterList>int</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#GetHeaderRange</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#GetRowDataPublic</CodeMapItemPath><ParameterList>int</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#GetLastColumnIndex</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法#GetWorksheet</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>frm查找站点#初始化#frm查找站点</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\HyVstoForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\HyVstoForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationConverterExample#ExecuteStationConversion</CodeMapItemPath><ParameterList>Workbook</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ClsAuthorization.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Extensions\MenuManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\FormManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Ribbon.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>Ribbon#IRibbonExtensibility 成员#GetCustomUI</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ExcelHelper#BatchUpdateMultiColumnResults</CodeMapItemPath><ParameterList>Range, object[,], ILogService</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorForm#构造函数#StationDataProcessorForm</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorForm#初始化方法#SetDefaultDataRange</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法#ConfirmProcessingOperation</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法#ShowProcessingOptionsSummary</CodeMapItemPath><ParameterList>Core.Interfaces.ProcessingOptions</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>frmExcelFileManager#frmExcelFileManager</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>frm51Helper#cookiesFile</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frm51Helper#frm51Helper</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frm51Helper#登录_Click</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frm51Helper#frm51Helper_Load</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>14</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frm51Helper#button上传设计交底_Click</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frm51Helper#获取登录信息</CodeMapItemPath><ParameterList/><UsageCount>15</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#ProcessStatusUpdated</CodeMapItemPath><ParameterList/><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#StationDataProcessor</CodeMapItemPath><ParameterList>Application, Worksheet</ParameterList><UsageCount>7</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#InitializeColumnIndices</CodeMapItemPath><ParameterList/><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#FindColumnByHeaderTitle</CodeMapItemPath><ParameterList>Worksheet, string</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataProcessor#ProcessData</CodeMapItemPath><ParameterList/><UsageCount>3</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\frm考勤.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\frm考勤.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>frm文件操作#界面控制#frm文件操作</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#ParseStationName</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#ConvertToFrequencyBand</CodeMapItemPath><ParameterList>object</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#GenerateStationFrequencyIndex</CodeMapItemPath><ParameterList>string, string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#AnalyzeStationData</CodeMapItemPath><ParameterList>List&lt;StationDataRow&gt;, string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\AssemblyLoadTracker.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\AssemblyLoadTracker.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>OrderDataExtractor#公共方法#FindColumnPositions</CodeMapItemPath><ParameterList>Worksheet, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w></ProjectAllTimeMostUsedData><ProjectExpandedStateData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>DataCacheManager#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#有效订单状态配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#列标题名称常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#运营商映射规则</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#工作表名称常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#数据处理配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#KML生成配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#验证配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlConfig#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorHelper#处理选项摘要</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorHelper#处理确认</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorHelper#核心处理执行</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorHelper#UI辅助功能</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorHelper#进度处理辅助</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>PerformanceOptimizer#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>AuthenticationService#数据模型（重新定义，不调用原版本）</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>AuthenticationService#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorHelper#事件定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorHelper#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorHelper#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorHelper#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorHelper#主要公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorHelper#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>事件参数类</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ThisAddIn.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#其它</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#常量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#定义变量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#VSTO 初始化#定时设置为只读</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#App窗体事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#App工作簿事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#App工作表事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#App单元格事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#窗体相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ThisAddIn#CustomTaskPane管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HyLicenseManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#常量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#静态字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#实现抽象方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#授权控制器初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#权限管理器初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#权限检查方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#权限刷新方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#用户组管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#网络授权更新回调</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#定时器和设置初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyLicenseManager#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frmPolygonGpsConverter#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frmPolygonGpsConverter#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frmPolygonGpsConverter#事件处理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frmPolygonGpsConverter#窗体事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#配置属性 - 直接替换原有硬编码</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#配置验证方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#配置加载方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#私有字段#性能优化缓存字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#缓存结构定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#IDataProcessor接口实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#错误处理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#列索引缓存方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#性能优化缓存方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#取消处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#动态控件标题管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#控件标题更正方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#hyTab</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#hyTab2</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#hyTab2#脚本</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#znTab &amp; hyTab</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#znTab &amp; hyTab#Hy常用文件列表</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyRibbonClass#znTab2</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>具体的批量操作实现类</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#控件事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\frm站点数据转换.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\frm站点数据转换.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#日志级别枚举</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>MainForm#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#初始化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HyUIPermissionManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HyUIPermissionManager#组合模式集成映射管理器</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frm工作表管理#变量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm工作表管理#初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>GlobalSettings.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Extensions\ZnWireless.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\ZnWireless.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#额外控件字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ProgressEventArgs</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ValidationResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ValidationResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>TaskManagementService#私有辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#线程安全日志方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#性能优化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#数据缓存方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#私有辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ExcelDataAccess#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>TowerAccountProcessorHelper#常量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>TowerAccountProcessorHelper#结果类定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>TowerAccountProcessorHelper#铁塔内部台账提取电信部分</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frmGPS生成图层#常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#额外控件字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#初始化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#处理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>Zn51Helper#变量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>Zn51Helper#辅助方法Public_网络相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#初始化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#窗体事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#配置绑定</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#日志显示初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#控件状态管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#用户交互增强</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#文件选择控件事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#按钮事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#输入验证</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#异步生成处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#KML生成助手事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#结果处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGenerator#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGenerator#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGenerator#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frm查找站点#常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm查找站点#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm查找站点#查找单个经纬度</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm查找站点#查找站点</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\HyVstoForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\HyVstoForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ClsAuthorization.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HyPermissionKeys</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#私有字段#性能优化缓存字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#缓存结构定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#优化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#错误处理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#列索引缓存方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#性能优化缓存方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessor#取消处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Extensions\MenuManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\FormManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Ribbon.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>Ribbon#功能区回调</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>BatchOperationTemplate#抽象方法 - 子类必须实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>BatchOperationTemplate#虚方法 - 子类可选择重写</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frm无线小工具#常量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm无线小工具#初始化及界面</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm无线小工具#提取方向角下倾角</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm无线小工具#铁塔内部台账梳理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorForm#额外控件字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#配置节名称常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#界面设置配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>StationDataProcessorConfig#列名配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>BatchOperationForm#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>BatchOperationForm#事件处理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>BatchOperationForm#批量操作执行</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>BatchOperationForm#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OrderKmlGeneratorForm#Windows Form Designer generated code</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\frm考勤.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\frm考勤.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#组件设计器生成的代码</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frm文件操作#查找文件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm文件操作#复制文件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm文件操作#修改时间</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frm文件操作#导入文件名</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\AssemblyLoadTracker.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\AssemblyLoadTracker.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>Zn51Helper#变量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>Zn51Helper#辅助方法Public_网络相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OrderDataExtractor#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w></ProjectExpandedStateData><ProjectFavoriteData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ThisAddIn.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HyLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\frm站点数据转换.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\frm站点数据转换.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HyUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>GlobalSettings.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Extensions\ZnWireless.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\ZnWireless.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ValidationResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ValidationResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\HyVstoForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\HyVstoForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ClsAuthorization.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Extensions\MenuManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\FormManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Ribbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\frm考勤.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\frm考勤.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\AssemblyLoadTracker.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\AssemblyLoadTracker.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w></ProjectFavoriteData><ProjectHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>675</Char><CodeMapItemPath>DataCacheManager#公共方法#IsFirstOccurrence</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T19:13:20.2031571+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>DataCacheManager#公共方法#RebuildRowMappingOnly</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-17T19:01:04.4059353+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>RowDataCache#RowNumber</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-17T19:00:36.7806823+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>RowDataCache#SectorName</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-17T19:00:50.8711575+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>RowDataCache#StationName</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-17T19:00:50.4151505+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>RowDataCache#FrequencyBand</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T19:00:49.810176+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>36</Char><CodeMapItemPath>OrderKmlConfig#工作表列映射配置#GetTowerSheetColumnTitles</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T14:38:45.9770184+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>141</Char><CodeMapItemPath>ApiClient#PostAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T23:39:34.2167377+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ApiClient#PostJsonObjectAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T23:41:02.0393879+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>601</Char><CodeMapItemPath>ApiClient#UploadFileAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-12T23:41:02.9391057+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ApiClient#UploadFileJsonObjectAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T23:41:03.5937903+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ApiClient#DownloadFileAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T23:41:04.2927924+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string, string, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>587</Char><CodeMapItemPath>frmPPTHelper#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T23:51:26.8341628+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>690</Char><CodeMapItemPath>StationGroupProcessor#GroupLogicalStations</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T23:53:41.0524706+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;LogicalStation&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>19</Char><CodeMapItemPath>StationGroupProcessor#GroupByGpsProximity</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T23:29:11.3761998+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;LogicalStation&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2465</Char><CodeMapItemPath>FileUploadService#UploadFileAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T23:33:18.379385+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>406</Char><CodeMapItemPath>ZnRibbonClass#hyRibbon1_Load</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-29T16:00:57.4141337+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonUIEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ZnRibbonClass#buttonPPT转PDF_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-29T17:08:32.549899+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>213</Char><CodeMapItemPath>ZnRibbonClass#buttonWord生成修改_Click</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-29T16:03:12.8247171+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>422</Char><CodeMapItemPath>ZnRibbonClass#button文件操作_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T17:13:56.9906541+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>-6</Char><CodeMapItemPath>ZnRibbonClass#button批量找文件_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T17:09:49.961437+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>KmlDescriptionConverter#ConvertKmlWithDescription</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T17:18:57.6626451+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>2495</Char><CodeMapItemPath>KmlDescriptionConverter#ProcessPlacemarks</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T16:32:50.6707899+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>XDocument</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>337</Char><CodeMapItemPath>StationDataProcessorHelper#配置文件管理#GetConfigDirectory</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:04:01.3904109+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>194</Char><CodeMapItemPath>StationDataProcessorHelper#配置文件管理#GetAvailableConfigFiles</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-27T14:03:20.8583978+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>163</Char><CodeMapItemPath>StationDataProcessorHelper#配置文件管理#OpenConfigDirectory</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-22T21:01:08.3207217+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1564</Char><CodeMapItemPath>StationDataProcessorHelper#核心处理执行#ExecuteProcessingAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T21:42:03.4368362+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, Range, string, System.Action&lt;string&gt;, IProgress&lt;ProgressEventArgs&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorHelper#核心处理执行#CancelProcessing</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T21:32:22.1731203+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Core.StationDataProcessor</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法#CreateThreadSafeLogWriter</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T21:32:10.2301403+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ETLogDisplayControl, Form</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法#CreateThreadSafeMessageHandler</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T21:32:13.0540061+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Form</ParameterList></HistoryDataItem><HistoryDataItem><Char>310</Char><CodeMapItemPath>StationDataProcessorHelper#简化的窗体辅助方法#ValidateBasicWorksheetAndRange</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-22T21:32:15.0057707+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, System.Action&lt;string, string, MessageBoxIcon&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorHelper#进度处理辅助#ProgressUpdateInfo#ProgressPercentage</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-22T21:32:18.1193878+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>21</Char><CodeMapItemPath>PerformanceOptimizer#性能优化方法#DisableOptimization</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T20:27:41.4751528+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>42</Char><CodeMapItemPath>PerformanceOptimizer#性能优化方法#GetMemoryUsage</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-19T20:27:24.8825436+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>84</Char><CodeMapItemPath>AuthenticationService#LoginAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T22:14:26.6367094+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Form</ParameterList></HistoryDataItem><HistoryDataItem><Char>246</Char><CodeMapItemPath>AuthenticationService#ValidateLoginAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T23:39:15.1060518+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>333</Char><CodeMapItemPath>AuthenticationService#GetUserInfoAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-12T17:43:21.8017914+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>366</Char><CodeMapItemPath>AuthenticationService#私有辅助方法#CheckWebView2Runtime</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-12T17:46:13.7061929+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>825</Char><CodeMapItemPath>AuthenticationService#私有辅助方法#GetLoginHeadersAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T23:38:53.3433924+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Form</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>24</Char><CodeMapItemPath>ITaskApplicationService#ExecuteBatchUploadAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T10:36:11.5125222+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>BatchUploadRequest, IProgress&lt;BatchProgress&gt;, CancellationToken</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>250</Char><CodeMapItemPath>OrderKmlGeneratorHelper#进度报告方法#ReportProgress</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T09:07:34.6622531+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>int, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ThisAddIn.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ThisAddIn#定义变量#ConfigurationSettings</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T23:22:14.2976599+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>851</Char><CodeMapItemPath>ThisAddIn#VSTO 初始化#ThisAddIn_Startup</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-29T19:09:27.8870032+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ThisAddIn#VSTO 初始化#EnsureMenuInitialized</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-02T21:14:04.7138617+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>222</Char><CodeMapItemPath>ThisAddIn#VSTO 初始化#InitializeExcelApplication</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-28T13:22:22.4518503+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>548</Char><CodeMapItemPath>ThisAddIn#VSTO 初始化#LoadConfigurationAndAuthorization</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:10:45.6287405+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HyLicenseManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyLicenseManager#实现抽象方法#GetLicenseControllerConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-07T09:21:45.2737384+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>203</Char><CodeMapItemPath>HyLicenseManager#权限检查方法#HasPermission</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-02T21:12:14.5334663+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyLicenseManager#用户组管理#RefreshCurrentUserGroupsInternal</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-07T09:24:22.2946712+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyLicenseManager#用户组管理#LoadUserGroupsFromConfig</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-07T09:24:11.1791015+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyLicenseManager#网络授权更新回调#OnNetworkLicenseUpdated</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-07T09:24:08.6628462+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ETLicenseInfo, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>627</Char><CodeMapItemPath>HyLicenseManager#授权状态刷新#RefreshAuthorizationAsyncInternal</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T11:53:45.0527295+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyLicenseManager#定时器和设置初始化#InitializeTimerAndSettings</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-07T09:24:01.7979687+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IAuthenticationService#LoginAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T22:13:40.7765233+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>System.Windows.Forms.Form</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>67</Char><CodeMapItemPath>frmPolygonGpsConverter#事件处理方法#btnConvert_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-26T00:43:53.7333704+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>244</Char><CodeMapItemPath>frmPolygonGpsConverter#私有辅助方法#TxtRangeSelect_Enter</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-26T00:44:52.8986238+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IDataProcessor#ProcessStationDataAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T08:57:45.7766272+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, ProcessingOptions</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>257</Char><CodeMapItemPath>StationDataProcessorForm#Windows 窗体设计器生成的代码#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T23:56:21.4688912+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1550</Char><CodeMapItemPath>StationDataProcessor#优化方法#SetColumnFormatsToText</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T15:38:12.1625894+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>2238</Char><CodeMapItemPath>StationDataProcessor#IDataProcessor接口实现#ProcessStationDataAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-20T13:52:43.5087966+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, ProcessingOptions</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessor#私有方法#CalculateStationFrequencyIndex</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:11.6375165+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessor#私有方法#CalculateLogicalStationFromCache</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:12.2718655+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>1380</Char><CodeMapItemPath>StationDataProcessor#列索引缓存方法#InitializeColumnIndexCache</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-20T11:35:03.6691521+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>84</Char><CodeMapItemPath>ApiResponse#Failure</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T22:23:23.1617008+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>983</Char><CodeMapItemPath>frm51Helper#获取登录信息</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T21:56:33.6737242+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>54</Char><CodeMapItemPath>frm51Helper#button登录_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-16T21:56:11.5642072+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>24</Char><CodeMapItemPath>HyRibbonClass#znTab &amp; hyTab#buttonHy51小工具V2_Click</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-23T09:01:10.284558+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>246</Char><CodeMapItemPath>HyRibbonClass#buttonZnHy文件记录管理_Click</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-23T10:40:07.8267413+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>77</Char><CodeMapItemPath>HyRibbonClass#buttonDevelopTest_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-26T00:44:58.3577643+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>466</Char><CodeMapItemPath>HyRibbonClass#button订单文件生成kml图层_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-23T22:25:11.4381625+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, RibbonControlEventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>292</Char><CodeMapItemPath>WorksPointInfo#WorksPointInfo</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T12:36:18.9650404+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>JsonObject</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>793</Char><CodeMapItemPath>frmDropdownInputForm#WndProc</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T20:49:31.1857107+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Message</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>228</Char><CodeMapItemPath>ConfigManager#ConfigManager</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:01:38.8595145+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Logging.ILogger, ExcelService</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>frm51Helper#下载交付列表并更新</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T08:59:58.7407919+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1875</Char><CodeMapItemPath>BatchOperationService#ApproveAllAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T17:45:43.9087595+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>234</Char><CodeMapItemPath>KmlConverterIntegration#ConvertSpecificKml</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T16:32:37.8988918+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>281</Char><CodeMapItemPath>ETRangeSelectControl#私有方法#ActiveExcelApp</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T23:42:21.6191872+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IExcelDataAccess#SortByColumnAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-18T16:25:59.6270628+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IExcelDataAccess#RefreshExcelDataAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-18T11:55:18.4119528+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>5</Char><CodeMapItemPath>IExcelDataAccess#SetAutoFilterAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:01.5854239+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>7</Char><CodeMapItemPath>IExcelDataAccess#GetDataRowCount</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:02.462457+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IExcelDataAccess#IsColumnExists</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:03.2092328+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>26</Char><CodeMapItemPath>IExcelDataAccess#GetRowDataPublic</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-18T09:39:47.7073897+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\frm站点数据转换.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\frm站点数据转换.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>43</Char><CodeMapItemPath>LogicalStation#GetHashCode</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T22:59:46.2198103+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>36572</Char><CodeMapItemPath>frm查找站点#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T00:08:52.6616622+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>29</Char><CodeMapItemPath>LogService#Info</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T22:14:51.2944143+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>30</Char><CodeMapItemPath>LogService#Debug</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T23:39:12.1803612+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLogDisplayControl#构造函数#ETLogDisplayControl</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T09:38:40.4854471+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>85</Char><CodeMapItemPath>MainForm#事件处理方法#ButtonDefaultConnectionTable_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T22:00:47.8166887+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>771</Char><CodeMapItemPath>Zn51Helper#DownloadTaskManagementFile</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T23:25:56.5915698+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, DateTime?, DateTime?</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>341</Char><CodeMapItemPath>TaskInfo#TaskInfo</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T12:33:22.6787214+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>JsonObject</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>578</Char><CodeMapItemPath>Program#ShowHelp</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T15:36:21.2207018+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>frm字符处理#tabControl1_SelectedIndexChanged</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-27T14:17:10.1264385+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>34</Char><CodeMapItemPath>frm字符处理#listView内置正则表达式_SelectedIndexChanged</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:17:17.1608152+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1691</Char><CodeMapItemPath>frmExcelFileManager#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T21:04:00.7024326+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HyUIPermissionManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1010</Char><CodeMapItemPath>HyControlMappingManager#GenerateControlTitleMappingDynamically</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T21:35:37.4861365+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#RegisterPermissionUIMappings</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-24T21:51:52.668967+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1733</Char><CodeMapItemPath>HyUIPermissionManager#SetDefaultUIVisibility</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-24T21:51:57.3242868+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>135</Char><CodeMapItemPath>HyUIPermissionManager#GetPermissionUIActions</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T22:05:56.2911147+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#GetPermissionDisplayName</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:09.1671419+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>1458</Char><CodeMapItemPath>HyUIPermissionManager#RegisterControlPermissionMappings</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-24T21:26:48.2479512+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#Initialize</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:19.5880294+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#ForceRefreshPermissionsAndUI</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:20.5492447+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#ForceInitializeGlobalMappings</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:24.6351385+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#GetGlobalControlTitleMapping</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:25.2987265+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法#OnRefreshRibbonControlTitles</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:48.3671728+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法#GetControlNormalTitleFallback</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:53.2040724+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyUIPermissionManager#重写基类的控件标题管理方法#GetControlPermissionKey</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-24T21:08:54.9575792+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1009</Char><CodeMapItemPath>frm工作表管理#SetSheetVisible</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-02T12:24:39.36768+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ListViewItem, XlSheetVisibility</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>GlobalSettings.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>43</Char><CodeMapItemPath>PhysicalStation#GetHashCode</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T22:59:46.7017036+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Extensions\ZnWireless.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\ZnWireless.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>737</Char><CodeMapItemPath>StationDataProcessorForm#事件处理#BtnProcess_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T21:29:38.3051352+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorForm#处理方法#SetProcessingState</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-22T16:30:53.9359053+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>1274</Char><CodeMapItemPath>StationDataProcessorForm#方向角提取功能#BtnExtractAngles_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T21:51:08.7014634+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>440</Char><CodeMapItemPath>StationDataProcessorForm#铁塔台账处理功能#BtnExtractTelecomPart_Click</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-22T19:00:36.7899227+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>1934</Char><CodeMapItemPath>StationDataProcessorForm#铁塔台账处理功能#BtnSummarizeToAuditAccount_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-22T21:23:46.2024826+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorConfig#配置属性 - 直接替换原有硬编码#SUFFIX_PATTERNS</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-20T13:33:01.3383322+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorConfig#配置属性 - 直接替换原有硬编码#SOURCE_COLUMNS</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-20T11:43:43.7959418+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorConfig#初始化方法#LoadAllConfigurations</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T13:33:06.1154854+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ETSectionConfigReader</ParameterList></HistoryDataItem><HistoryDataItem><Char>263</Char><CodeMapItemPath>StationDataProcessorConfig#配置加载方法#LoadSourceColumns</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-19T23:21:39.2087613+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ETSectionConfigReader</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>219</Char><CodeMapItemPath>KmlConverterHelper#ConvertKml</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T17:18:52.1240837+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>363</Char><CodeMapItemPath>FileInfo#FileInfo</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T12:36:31.2657577+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>JsonObject</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ValidationResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ValidationResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2351</Char><CodeMapItemPath>TaskManagementService#InitializeTaskAsync</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-12T12:36:58.8269487+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WorksPointInfo, string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>977</Char><CodeMapItemPath>TaskManagementService#InitializeBudgetTaskAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-12T13:55:07.9554062+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WorksPointInfo, double, double, double</ParameterList></HistoryDataItem><HistoryDataItem><Char>932</Char><CodeMapItemPath>TaskManagementService#GetDynamicFormFieldsAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-12T16:26:33.3818755+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>523</Char><CodeMapItemPath>TaskManagementService#IsTaskCompletedAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T17:43:21.6928993+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WorksPointInfo, string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>TaskManagementService#扩展功能方法#GenerateDefaultConnectionTableAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T22:03:10.4974627+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1200</Char><CodeMapItemPath>WorksPointService#GetWorksPointBaseInfoAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T23:32:57.4076518+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, CancellationToken</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ExcelDataAccess#私有字段#_dataCache</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-19T20:26:58.0789499+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>5647</Char><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#AddCalculatedColumnsAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T15:42:23.1144739+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Dictionary&lt;string, Func&lt;object[], object&gt;&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#SetAutoFilterAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-20T15:21:44.22584+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>887</Char><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#GetDataRowCount</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-19T21:21:25.1174189+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>663</Char><CodeMapItemPath>ExcelDataAccess#IExcelDataAccess接口实现#SetCellValue</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-20T00:03:12.187841+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>int, int, object</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1237</Char><CodeMapItemPath>TowerAccountProcessorHelper#辅助方法#SummarizeData</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T22:21:39.1415276+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>70</Char><CodeMapItemPath>frmGPS生成图层#事件处理#button生成KML_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T23:01:15.4292838+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>958</Char><CodeMapItemPath>frmGPS生成图层#KML生成方法#GeneratePointKml</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T23:19:20.3550627+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, Range, Range, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>1213</Char><CodeMapItemPath>frmGPS生成图层#KML转换功能#BtnKmlConvert_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-22T20:13:07.1594921+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>850</Char><CodeMapItemPath>StationDataProcessorForm#事件处理#BtnCreateWorkbook_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-21T22:55:04.1301944+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>101</Char><CodeMapItemPath>StationDataProcessorForm#事件处理#BtnExecuteConversion_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T23:03:44.2764821+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>frmTopMostForm#Start</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-21T17:31:41.3308477+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1762</Char><CodeMapItemPath>FileUploadService#UploadFileOnlyAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T12:36:07.9978135+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>4105</Char><CodeMapItemPath>FileUploadService#私有辅助方法#UpdateBudgetServerStatusAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T12:39:09.1148776+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>TaskContext, FileInfo, double, double, double</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>763</Char><CodeMapItemPath>frm文本复制粘贴辅助框#ListView1_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-26T15:42:50.4908845+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>569</Char><CodeMapItemPath>OrderKmlGeneratorForm#资源清理#CleanupResources</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T21:02:01.4666631+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>460</Char><CodeMapItemPath>OrderKmlGeneratorForm#按钮事件处理#btnGenerate_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-23T11:01:18.4289986+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>52</Char><CodeMapItemPath>frm查找站点#初始化#frm查找站点</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-16T23:20:38.5469842+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>72</Char><CodeMapItemPath>frm查找站点#查找站点#button查找站点_执行_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-20T19:54:32.6012825+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>58</Char><CodeMapItemPath>frm查找站点#查找多边形#ucERS查找多边形_执行_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-23T00:01:33.6964947+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>329</Char><CodeMapItemPath>frm查找站点#查找多边形#UpdatePolygonSearchModeVisibility</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T11:20:41.2014805+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\HyVstoForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\HyVstoForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationConverterExample#ExecuteStationConversion</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T15:28:34.1048069+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Workbook</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>849</Char><CodeMapItemPath>StationDataConverter#ConvertStationData</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-21T23:16:23.8091261+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, Worksheet, Range, Range</ParameterList></HistoryDataItem><HistoryDataItem><Char>320</Char><CodeMapItemPath>StationDataConverter#ProcessLogicalStations</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T23:23:46.4352932+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;LogicalStation&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>297</Char><CodeMapItemPath>CookieManagerAdapter#RefreshLoginInfoAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T23:30:31.2602983+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>104</Char><CodeMapItemPath>CookieManagerAdapter#ValidateLoginStatusAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-11T23:27:16.6172616+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ClsAuthorization.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>963</Char><CodeMapItemPath>StationDataProcessor#私有方法#ValidateDataAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T21:25:30.8231412+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet</ParameterList></HistoryDataItem><HistoryDataItem><Char>631</Char><CodeMapItemPath>StationDataProcessor#私有方法#AddStatisticsColumnsWithRowContext</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-22T00:16:55.5152705+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>37</Char><CodeMapItemPath>StationDataProcessor#私有方法#CalculateDowntiltMergedWithRowContext</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T00:29:28.3272111+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object[], int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Extensions\MenuManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>MenuManager#RebuildAllMenus</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T11:54:25.6001971+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\FormManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>576</Char><CodeMapItemPath>FormManager#ShowForm</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-21T17:30:57.1417221+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Form, XlFormPosition</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>96</Char><CodeMapItemPath>TaskContext#TaskContext</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T12:35:41.1748256+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>375</Char><CodeMapItemPath>TaskContext#FindDynamicField</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T12:36:25.2947929+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Ribbon.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>Ribbon#IRibbonExtensibility 成员#GetCustomUI</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-07T09:55:15.8830964+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>373</Char><CodeMapItemPath>frm台账扇区汇总#btnProcess_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T16:11:58.9430187+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ExcelHelper#BatchUpdateMultiColumnResults</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-12T23:43:02.3024672+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, object[,], ILogService</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ExcelHelper#ValidateRangeSelection</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T23:43:03.8769051+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, int, string, ILogService</ParameterList></HistoryDataItem><HistoryDataItem><Char>276</Char><CodeMapItemPath>ExcelHelper#GetCellDoubleValue</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-12T21:57:30.0328623+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, double</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>249</Char><CodeMapItemPath>BatchOperationTemplate#通用辅助方法#GetCellDoubleValue</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T17:46:51.3494453+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>322</Char><CodeMapItemPath>KmlConverterExample#ShowConversionEffect</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T16:33:14.8580661+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>78</Char><CodeMapItemPath>frm无线小工具#btn台账扇区汇总_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T20:13:19.4582071+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>41</Char><CodeMapItemPath>StationDataProcessorForm#初始化方法#InitializeAdditionalControls</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-20T00:03:01.9368574+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>349</Char><CodeMapItemPath>StationDataProcessorForm#初始化方法#InitializeProcessor</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-20T09:42:28.6962967+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>64</Char><CodeMapItemPath>StationDataProcessorForm#初始化方法#LoadConfiguration</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-19T20:25:48.3011356+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1011</Char><CodeMapItemPath>StationDataProcessorForm#事件处理#BtnProcess_Click</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-19T20:25:41.0696583+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>1192</Char><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法#ValidateDataRange</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T09:42:11.6001554+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法#ConfirmProcessingOperation</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:08.9051728+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessorForm#用户体验优化方法#ShowProcessingOptionsSummary</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T15:34:09.6384343+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Core.Interfaces.ProcessingOptions</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1464</Char><CodeMapItemPath>frmKmlConverter#btnSelectSource_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T15:21:59.4943042+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>69</Char><CodeMapItemPath>frmKmlConverter#btnConvert_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T17:18:36.7094354+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>14</Char><CodeMapItemPath>StationDataProcessorConfig#公共方法#ReloadConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T20:25:15.1922273+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>147</Char><CodeMapItemPath>BatchOperationForm#初始化方法#BatchOperationForm_Load</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T14:11:39.7719259+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>627</Char><CodeMapItemPath>OrderKmlGeneratorForm#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T17:19:51.2556981+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>261</Char><CodeMapItemPath>StationDataProcessorForm#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-18T00:37:02.544772+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>frmExcelFileManager#frmExcelFileManager</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T21:07:47.3910976+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>12361</Char><CodeMapItemPath>StationDataProcessorForm#Windows 窗体设计器生成的代码#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T22:51:46.8587769+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>402</Char><CodeMapItemPath>PolygonGpsConverterHelper#ParsePolygonCoordinates</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-26T00:46:01.2737533+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ITaskManagementService#GenerateDefaultConnectionTableAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T22:02:27.3483851+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>frm51Helper#cookiesFile</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T08:55:31.8448718+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>frm51Helper#frm51Helper</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-11T08:55:27.4361815+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>frm51Helper#登录_Click</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-10T22:59:30.0206897+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>601</Char><CodeMapItemPath>frm51Helper#frm51Helper_Load</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T08:58:11.2623522+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>frm51Helper#button上传设计交底_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-10T23:00:01.5050275+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>2890</Char><CodeMapItemPath>frm51Helper#获取登录信息</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T10:25:39.7985185+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>14</Char><CodeMapItemPath>frm51Helper#button登录_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T23:37:50.6457911+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>278</Char><CodeMapItemPath>TestConverter#ShowExample</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T16:33:06.844974+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2356</Char><CodeMapItemPath>BatchOperationForm#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T21:45:27.8654205+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>273</Char><CodeMapItemPath>DataValidationException#DataValidationException</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T09:28:58.0576738+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string, string, string, string, DateTime?, Exception</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>129</Char><CodeMapItemPath>HyRibbonClass#HyRibbonClass</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T16:08:09.5446805+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>19403</Char><CodeMapItemPath>HyRibbonClass#组件设计器生成的代码#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T23:11:42.6801137+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>9</Char><CodeMapItemPath>HyRibbonClass#btn填写合规检查</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T16:08:59.3964025+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HyRibbonClass#buttonini配置文件</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T17:00:39.8822355+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessor#ProcessStatusUpdated</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-18T11:51:12.6548415+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>768</Char><CodeMapItemPath>StationDataProcessor#StationDataProcessor</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-19T16:04:54.5814753+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Application, Worksheet</ParameterList></HistoryDataItem><HistoryDataItem><Char>1022</Char><CodeMapItemPath>StationDataProcessor#LoadHeaderConfiguration</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T16:11:17.4915962+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>676</Char><CodeMapItemPath>StationDataProcessor#LoadDefaultHeaderConfiguration</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-19T16:06:32.8224932+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>36</Char><CodeMapItemPath>StationDataProcessor#InitializeTargetHeaders</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T16:08:45.4964023+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessor#InitializeColumnIndices</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-17T09:30:59.106351+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessor#FindColumnByHeaderTitle</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T09:30:11.9086738+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>StationDataProcessor#ProcessData</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-18T15:53:21.4818032+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>139</Char><CodeMapItemPath>StationDataProcessor#FormatTargetWorksheet</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-19T15:57:56.691787+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>259</Char><CodeMapItemPath>frm字符处理#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:21:37.7854887+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\frm考勤.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\frm考勤.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2444</Char><CodeMapItemPath>frm51Helper#下载任务管理并更新</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T15:16:07.870057+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>494</Char><CodeMapItemPath>BatchOperationService#ExecuteBatchAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T10:36:00.60519+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>IEnumerable&lt;TInput&gt;, Func&lt;TInput, Task&lt;TOutput&gt;&gt;, BatchExecutionOptions, IProgress&lt;BatchProgress&gt;, CancellationToken</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLogDisplayControl#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T00:11:51.9495984+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>300</Char><CodeMapItemPath>frm51HelperV2#初始化方法#InitializeServices</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-11T22:11:57.484243+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>69</Char><CodeMapItemPath>frm51HelperV2#窗体事件处理#frm51HelperV2_Load</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-11T22:08:30.2300156+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>121</Char><CodeMapItemPath>frm51HelperV2#窗体事件处理#frm51HelperV2_FormClosing</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-11T21:52:33.5172117+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>96</Char><CodeMapItemPath>frm文件操作#界面控制#frm文件操作</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T22:52:11.7760613+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>51</Char><CodeMapItemPath>frm文件操作#界面控制#SetupExcelRangeSelectors</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-13T20:16:43.2223663+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>228</Char><CodeMapItemPath>frm文件操作#界面控制#LoadPresetDirectories</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:01:35.359471+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>54</Char><CodeMapItemPath>StationDataAnalyzer#构造函数#StationDataAnalyzer</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T22:01:54.9573387+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>552</Char><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#ParseStationName</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T11:44:01.3822493+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>734</Char><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#GenerateStationFrequencyIndex</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-17T14:49:35.2462175+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>2397</Char><CodeMapItemPath>StationDataAnalyzer#IDataAnalyzer接口实现#AnalyzeStationData</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-17T15:06:17.9309356+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;StationDataRow&gt;, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>266</Char><CodeMapItemPath>StationDataAnalyzer#公共辅助方法#HasRruSuffix</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T22:24:31.479311+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>408</Char><CodeMapItemPath>PolygonGpsConverterEntry#ProcessCurrentSelection</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-26T00:47:03.2525927+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>67</Char><CodeMapItemPath>StationConverterHelper#ExecuteAutoConversion</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-21T23:03:59.4935181+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\AssemblyLoadTracker.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\AssemblyLoadTracker.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>258</Char><CodeMapItemPath>Zn51Helper#辅助方法Public_日志/文件相关#TryLoadNewFormatCookies</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T11:34:08.654131+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Dictionary&lt;string, string&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>71</Char><CodeMapItemPath>frm备份及发送#button生成发送存档_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-26T20:53:08.4377158+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>718</Char><CodeMapItemPath>OrderDataExtractor#公共方法#ExtractFromTowerSheet</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T16:09:34.3794579+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>1127</Char><CodeMapItemPath>OrderDataExtractor#公共方法#FindColumnPositions</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-23T11:59:28.3831034+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>1024</Char><CodeMapItemPath>OrderDataExtractor#私有方法#ExtractDataFromWorksheet</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T11:56:02.0229101+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, Dictionary&lt;string, int&gt;, string, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>293</Char><CodeMapItemPath>OrderDataExtractor#私有方法#GetCellDoubleValue</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T10:45:02.873187+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Worksheet, int, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2354</Char><CodeMapItemPath>frmPolygonGpsConverter#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-26T00:40:55.3629088+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>77</Char><CodeMapItemPath>AngleExtractorHelper#ValidateParameters</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T21:57:31.4091175+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ExtractionParameters</ParameterList></HistoryDataItem><HistoryDataItem><Char>885</Char><CodeMapItemPath>AngleExtractorHelper#ExtractAngles</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-22T21:49:16.0947454+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ExtractionParameters</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>436</Char><CodeMapItemPath>frmAIv2#SetDefaultRanges</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T13:51:18.6905525+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>72</Char><CodeMapItemPath>frmAIv2#ButtonExecute_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-21T22:40:07.0305978+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>205</Char><CodeMapItemPath>frmAIv2#AppendLog</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:04:09.6704802+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>657</Char><CodeMapItemPath>frmCrosshairOverlayForm#WndProc</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T22:07:16.3986748+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Message</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w></ProjectHistoryData><ProjectInCodeHighlightData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ThisAddIn.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HyLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\frm站点数据转换.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\frm站点数据转换.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HyUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>GlobalSettings.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Extensions\ZnWireless.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\ZnWireless.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ValidationResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ValidationResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\HyVstoForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\HyVstoForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ClsAuthorization.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Extensions\MenuManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\FormManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Ribbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\frm考勤.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\frm考勤.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\AssemblyLoadTracker.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\AssemblyLoadTracker.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w></ProjectInCodeHighlightData><ProjectMiniViewData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ZnRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ThisAddIn.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HyLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ErrorHandlingConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\frm站点数据转换.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\frm站点数据转换.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HyUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>GlobalSettings.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Extensions\ZnWireless.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\ZnWireless.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\BasicCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ValidationResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ValidationResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\HyVstoForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\HyVstoForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ILogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\SimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ClsAuthorization.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Extensions\MenuManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\FormManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Ribbon.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\Controls\LogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\Controls\LogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\ExceptionHandlingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HyRibbon.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Wenzi\frm考勤.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Wenzi\frm考勤.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\AssemblyLoadTracker.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\AssemblyLoadTracker.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\ISimpleCache.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Module.WX\51HelperV2\Core\Interfaces\IFileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w></ProjectMiniViewData><ProjectName>HyExcelVsto</ProjectName></ProjectData>